package com.operationrecorder.app.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.operationrecorder.app.databinding.ItemScriptBinding
import com.operationrecorder.app.model.OperationScript
import java.text.SimpleDateFormat
import java.util.*

class ScriptAdapter(
    private val scripts: List<OperationScript>,
    private val onExecuteClick: (OperationScript) -> Unit,
    private val onEditClick: ((OperationScript) -> Unit)? = null,
    private val onDeleteClick: ((OperationScript) -> Unit)? = null
) : RecyclerView.Adapter<ScriptAdapter.ScriptViewHolder>() {

    private val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ScriptViewHolder {
        val binding = ItemScriptBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ScriptViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ScriptViewHolder, position: Int) {
        holder.bind(scripts[position])
    }

    override fun getItemCount(): Int = scripts.size

    inner class ScriptViewHolder(
        private val binding: ItemScriptBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(script: OperationScript) {
            binding.apply {
                // 设置脚本基本信息
                tvScriptName.text = script.name
                tvScriptDescription.text = script.description.ifEmpty { "无描述" }
                tvScriptTime.text = dateFormat.format(Date(script.createdTime))
                
                // 设置统计信息
                tvOperationsCount.text = "操作数: ${script.operations.size}"
                tvDuration.text = "时长: ${formatDuration(script.duration)}"
                tvFileSize.text = "大小: ${calculateScriptSize(script)}KB"
                
                // 设置按钮点击事件
                btnExecute.setOnClickListener {
                    onExecuteClick(script)
                }
                
                btnEdit.setOnClickListener {
                    onEditClick?.invoke(script)
                }
                
                btnDelete.setOnClickListener {
                    onDeleteClick?.invoke(script)
                }
                
                // 如果没有提供编辑和删除回调，隐藏对应按钮
                if (onEditClick == null) {
                    btnEdit.visibility = android.view.View.GONE
                }
                if (onDeleteClick == null) {
                    btnDelete.visibility = android.view.View.GONE
                }
            }
        }
        
        private fun formatDuration(durationMs: Long): String {
            val seconds = durationMs / 1000
            return when {
                seconds < 60 -> "${seconds}s"
                seconds < 3600 -> "${seconds / 60}m ${seconds % 60}s"
                else -> "${seconds / 3600}h ${(seconds % 3600) / 60}m"
            }
        }
        
        private fun calculateScriptSize(script: OperationScript): Int {
            // 简单估算脚本的JSON大小
            val baseSize = 200 // 基础字段大小
            val operationSize = script.operations.size * 150 // 每个操作约150字节
            val textSize = script.operations.sumOf { it.text.length * 2 } // 文本内容
            
            return (baseSize + operationSize + textSize) / 1024 // 转换为KB
        }
    }
}
