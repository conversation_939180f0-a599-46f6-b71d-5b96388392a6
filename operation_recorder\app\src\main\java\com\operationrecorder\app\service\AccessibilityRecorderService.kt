package com.operationrecorder.app.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.Path
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.operationrecorder.app.model.Operation
import com.operationrecorder.app.model.OperationScript
import com.operationrecorder.app.model.OperationType
import com.operationrecorder.app.utils.ScriptManager
import java.text.SimpleDateFormat
import java.util.*

class AccessibilityRecorderService : AccessibilityService() {
    
    companion object {
        private const val TAG = "AccessibilityRecorder"
        private var instance: AccessibilityRecorderService? = null
        private var isRecording = false
        private var isExecuting = false
        
        fun isAccessibilityServiceEnabled(context: Context): Boolean {
            val accessibilityEnabled = Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED, 0
            )
            
            if (accessibilityEnabled == 1) {
                val services = Settings.Secure.getString(
                    context.contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
                )
                return services?.contains(context.packageName) == true
            }
            return false
        }
        
        fun startRecording() {
            instance?.let {
                isRecording = true
                it.startNewRecording()
            }
        }
        
        fun stopRecording() {
            instance?.let {
                isRecording = false
                it.stopCurrentRecording()
            }
        }
        
        fun executeScript(script: OperationScript) {
            instance?.let {
                if (!isExecuting) {
                    it.executeOperationScript(script)
                }
            }
        }
    }
    
    private val recordedOperations = mutableListOf<Operation>()
    private var recordingStartTime = 0L
    private var currentScriptName = ""
    private lateinit var scriptManager: ScriptManager
    private val handler = Handler(Looper.getMainLooper())
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        scriptManager = ScriptManager(this)
        Log.d(TAG, "AccessibilityRecorderService created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Log.d(TAG, "AccessibilityRecorderService destroyed")
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null || !isRecording) return
        
        try {
            when (event.eventType) {
                AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                    recordClickEvent(event)
                }
                AccessibilityEvent.TYPE_VIEW_LONG_CLICKED -> {
                    recordLongClickEvent(event)
                }
                AccessibilityEvent.TYPE_VIEW_SCROLLED -> {
                    recordScrollEvent(event)
                }
                AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                    recordTextInputEvent(event)
                }
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    recordWindowChangeEvent(event)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error recording accessibility event", e)
        }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "AccessibilityRecorderService interrupted")
    }
    
    private fun startNewRecording() {
        recordedOperations.clear()
        recordingStartTime = System.currentTimeMillis()
        
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        currentScriptName = "录制_${dateFormat.format(Date())}"
        
        Toast.makeText(this, "开始录制操作", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "Started new recording: $currentScriptName")
    }
    
    private fun stopCurrentRecording() {
        if (recordedOperations.isNotEmpty()) {
            val duration = System.currentTimeMillis() - recordingStartTime
            val script = OperationScript(
                name = currentScriptName,
                description = "自动录制的操作脚本",
                createdTime = recordingStartTime,
                operations = recordedOperations.toList(),
                duration = duration
            )
            
            scriptManager.saveScript(script)
            Toast.makeText(this, "录制完成，已保存脚本", Toast.LENGTH_SHORT).show()
            Log.d(TAG, "Recording stopped, saved ${recordedOperations.size} operations")
        } else {
            Toast.makeText(this, "没有录制到任何操作", Toast.LENGTH_SHORT).show()
        }
        
        recordedOperations.clear()
    }
    
    private fun recordClickEvent(event: AccessibilityEvent) {
        val nodeInfo = event.source ?: return
        val bounds = android.graphics.Rect()
        nodeInfo.getBoundsInScreen(bounds)
        
        val operation = Operation(
            type = OperationType.CLICK,
            timestamp = System.currentTimeMillis() - recordingStartTime,
            x = bounds.centerX().toFloat(),
            y = bounds.centerY().toFloat(),
            text = nodeInfo.text?.toString() ?: "",
            packageName = event.packageName?.toString() ?: "",
            className = event.className?.toString() ?: "",
            contentDescription = nodeInfo.contentDescription?.toString() ?: "",
            viewId = nodeInfo.viewIdResourceName ?: ""
        )
        
        recordedOperations.add(operation)
        Log.d(TAG, "Recorded click at (${operation.x}, ${operation.y})")
    }
    
    private fun recordLongClickEvent(event: AccessibilityEvent) {
        val nodeInfo = event.source ?: return
        val bounds = android.graphics.Rect()
        nodeInfo.getBoundsInScreen(bounds)
        
        val operation = Operation(
            type = OperationType.LONG_CLICK,
            timestamp = System.currentTimeMillis() - recordingStartTime,
            x = bounds.centerX().toFloat(),
            y = bounds.centerY().toFloat(),
            text = nodeInfo.text?.toString() ?: "",
            packageName = event.packageName?.toString() ?: "",
            className = event.className?.toString() ?: "",
            contentDescription = nodeInfo.contentDescription?.toString() ?: "",
            viewId = nodeInfo.viewIdResourceName ?: ""
        )
        
        recordedOperations.add(operation)
        Log.d(TAG, "Recorded long click at (${operation.x}, ${operation.y})")
    }
    
    private fun recordScrollEvent(event: AccessibilityEvent) {
        val operation = Operation(
            type = OperationType.SCROLL,
            timestamp = System.currentTimeMillis() - recordingStartTime,
            packageName = event.packageName?.toString() ?: "",
            className = event.className?.toString() ?: ""
        )
        
        recordedOperations.add(operation)
        Log.d(TAG, "Recorded scroll event")
    }
    
    private fun recordTextInputEvent(event: AccessibilityEvent) {
        val text = event.text?.joinToString("") ?: ""
        if (text.isNotEmpty()) {
            val operation = Operation(
                type = OperationType.INPUT_TEXT,
                timestamp = System.currentTimeMillis() - recordingStartTime,
                text = text,
                packageName = event.packageName?.toString() ?: "",
                className = event.className?.toString() ?: ""
            )
            
            recordedOperations.add(operation)
            Log.d(TAG, "Recorded text input: $text")
        }
    }
    
    private fun recordWindowChangeEvent(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: ""
        if (packageName.isNotEmpty() && packageName != this.packageName) {
            val operation = Operation(
                type = OperationType.APP_LAUNCH,
                timestamp = System.currentTimeMillis() - recordingStartTime,
                packageName = packageName,
                className = event.className?.toString() ?: ""
            )

            recordedOperations.add(operation)
            Log.d(TAG, "Recorded app launch: $packageName")
        }
    }

    private fun executeOperationScript(script: OperationScript) {
        if (isExecuting) {
            Toast.makeText(this, "正在执行其他脚本，请稍后", Toast.LENGTH_SHORT).show()
            return
        }

        isExecuting = true
        Toast.makeText(this, "开始执行脚本: ${script.name}", Toast.LENGTH_SHORT).show()

        Thread {
            try {
                for (operation in script.operations) {
                    if (!isExecuting) break // 允许中断执行

                    // 执行前等待
                    if (operation.delayBefore > 0) {
                        Thread.sleep(operation.delayBefore)
                    }

                    // 执行操作
                    handler.post {
                        executeOperation(operation)
                    }

                    // 执行后等待
                    Thread.sleep(operation.delayAfter)
                }

                handler.post {
                    Toast.makeText(this, "脚本执行完成", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing script", e)
                handler.post {
                    Toast.makeText(this, "脚本执行出错: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            } finally {
                isExecuting = false
            }
        }.start()
    }

    private fun executeOperation(operation: Operation) {
        try {
            when (operation.type) {
                OperationType.CLICK -> {
                    performClick(operation.x, operation.y)
                }
                OperationType.LONG_CLICK -> {
                    performLongClick(operation.x, operation.y)
                }
                OperationType.SWIPE -> {
                    // 滑动操作需要额外的参数，这里简化处理
                    performSwipe(operation.x, operation.y, operation.x + 100, operation.y + 100)
                }
                OperationType.INPUT_TEXT -> {
                    performTextInput(operation.text)
                }
                OperationType.APP_LAUNCH -> {
                    launchApp(operation.packageName)
                }
                OperationType.WAIT -> {
                    // 等待操作在外层循环中处理
                }
                else -> {
                    Log.w(TAG, "Unsupported operation type: ${operation.type}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing operation: ${operation.type}", e)
        }
    }

    private fun performClick(x: Float, y: Float) {
        val path = Path()
        path.moveTo(x, y)

        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 100))

        dispatchGesture(gestureBuilder.build(), null, null)
        Log.d(TAG, "Performed click at ($x, $y)")
    }

    private fun performLongClick(x: Float, y: Float) {
        val path = Path()
        path.moveTo(x, y)

        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 1000))

        dispatchGesture(gestureBuilder.build(), null, null)
        Log.d(TAG, "Performed long click at ($x, $y)")
    }

    private fun performSwipe(startX: Float, startY: Float, endX: Float, endY: Float) {
        val path = Path()
        path.moveTo(startX, startY)
        path.lineTo(endX, endY)

        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 300))

        dispatchGesture(gestureBuilder.build(), null, null)
        Log.d(TAG, "Performed swipe from ($startX, $startY) to ($endX, $endY)")
    }

    private fun performTextInput(text: String) {
        // 查找当前焦点的输入框
        val rootNode = rootInActiveWindow
        val focusedNode = findFocusedEditText(rootNode)

        focusedNode?.let { node ->
            val arguments = android.os.Bundle()
            arguments.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
            Log.d(TAG, "Performed text input: $text")
        } ?: run {
            Log.w(TAG, "No focused EditText found for text input")
        }
    }

    private fun findFocusedEditText(node: AccessibilityNodeInfo?): AccessibilityNodeInfo? {
        if (node == null) return null

        if (node.isFocused && node.className == "android.widget.EditText") {
            return node
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            val result = findFocusedEditText(child)
            if (result != null) return result
        }

        return null
    }

    private fun launchApp(packageName: String) {
        try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.let {
                it.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(it)
                Log.d(TAG, "Launched app: $packageName")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error launching app: $packageName", e)
        }
    }
}
