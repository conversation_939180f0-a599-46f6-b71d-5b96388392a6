package com.operationrecorder.app.model

import com.google.gson.annotations.SerializedName
import java.util.*

/**
 * 操作脚本数据模型
 */
data class OperationScript(
    @SerializedName("id")
    val id: String = UUID.randomUUID().toString(),
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("description")
    val description: String = "",
    
    @SerializedName("created_time")
    val createdTime: Long = System.currentTimeMillis(),
    
    @SerializedName("operations")
    val operations: List<Operation> = emptyList(),
    
    @SerializedName("duration")
    val duration: Long = 0L // 录制总时长（毫秒）
)

/**
 * 单个操作数据模型
 */
data class Operation(
    @SerializedName("type")
    val type: OperationType,
    
    @SerializedName("timestamp")
    val timestamp: Long, // 相对于录制开始的时间戳
    
    @SerializedName("x")
    val x: Float = 0f,
    
    @SerializedName("y")
    val y: Float = 0f,
    
    @SerializedName("text")
    val text: String = "",
    
    @SerializedName("package_name")
    val packageName: String = "",
    
    @SerializedName("class_name")
    val className: String = "",
    
    @SerializedName("content_description")
    val contentDescription: String = "",
    
    @SerializedName("view_id")
    val viewId: String = "",
    
    @SerializedName("delay_before")
    val delayBefore: Long = 0L, // 执行前等待时间
    
    @SerializedName("delay_after")
    val delayAfter: Long = 500L // 执行后等待时间
)

/**
 * 操作类型枚举
 */
enum class OperationType {
    @SerializedName("click")
    CLICK,
    
    @SerializedName("long_click")
    LONG_CLICK,
    
    @SerializedName("swipe")
    SWIPE,
    
    @SerializedName("scroll")
    SCROLL,
    
    @SerializedName("input_text")
    INPUT_TEXT,
    
    @SerializedName("key_event")
    KEY_EVENT,
    
    @SerializedName("wait")
    WAIT,
    
    @SerializedName("screenshot")
    SCREENSHOT,
    
    @SerializedName("app_launch")
    APP_LAUNCH,
    
    @SerializedName("app_close")
    APP_CLOSE
}

/**
 * 滑动操作的扩展数据
 */
data class SwipeOperation(
    val startX: Float,
    val startY: Float,
    val endX: Float,
    val endY: Float,
    val duration: Long = 300L
)

/**
 * 按键事件的扩展数据
 */
data class KeyEventOperation(
    val keyCode: Int,
    val action: Int // KeyEvent.ACTION_DOWN, KeyEvent.ACTION_UP
)
