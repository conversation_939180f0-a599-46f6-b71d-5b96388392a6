// This file is auto-generated by Android Data Binding.
// Do not modify this file -- YOUR CHANGES WILL BE ERASED!
package com.operationrecorder.app.databinding

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.operationrecorder.app.R

class ActivityMainBinding private constructor(
    private val rootView: View,
    val btnStartRecording: Button,
    val btnStopRecording: Button,
    val btnRefreshScripts: Button,
    val recyclerViewScripts: RecyclerView
) : ViewBinding {

    override fun getRoot(): View = rootView

    companion object {
        fun inflate(inflater: LayoutInflater): ActivityMainBinding {
            return inflate(inflater, null, false)
        }

        fun inflate(inflater: LayoutInflater, parent: ViewGroup?, attachToParent: Boolean): ActivityMainBinding {
            val root = inflater.inflate(R.layout.activity_main, parent, attachToParent)
            return bind(root)
        }

        fun bind(rootView: View): ActivityMainBinding {
            val btnStartRecording = rootView.findViewById<Button>(R.id.btn_start_recording)
            val btnStopRecording = rootView.findViewById<Button>(R.id.btn_stop_recording)
            val btnRefreshScripts = rootView.findViewById<Button>(R.id.btn_refresh_scripts)
            val recyclerViewScripts = rootView.findViewById<RecyclerView>(R.id.recycler_view_scripts)

            return ActivityMainBinding(rootView, btnStartRecording, btnStopRecording, btnRefreshScripts, recyclerViewScripts)
        }
    }
}
