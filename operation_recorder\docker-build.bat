@echo off
echo ========================================
echo    使用Docker构建Android APK
echo ========================================
echo.

echo 检查Docker是否可用...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    echo 请安装Docker Desktop并确保其正在运行
    pause
    exit /b 1
)

echo Docker检查通过
echo.

echo 构建Docker镜像...
docker build -t android-builder .

if %errorlevel% neq 0 (
    echo 错误: Docker镜像构建失败
    pause
    exit /b 1
)

echo.
echo 从Docker容器中复制APK文件...
docker create --name temp-container android-builder
docker cp temp-container:/app/app/build/outputs/apk/debug/app-debug.apk ./app-debug.apk
docker rm temp-container

if exist "app-debug.apk" (
    echo.
    echo ========================================
    echo           构建成功！
    echo ========================================
    echo.
    echo APK文件已复制到当前目录: app-debug.apk
    echo.
    for %%A in ("app-debug.apk") do echo 文件大小: %%~zA 字节
    echo.
    echo 安装命令:
    echo adb install app-debug.apk
) else (
    echo 错误: APK文件复制失败
)

echo.
echo 按任意键退出...
pause >nul
