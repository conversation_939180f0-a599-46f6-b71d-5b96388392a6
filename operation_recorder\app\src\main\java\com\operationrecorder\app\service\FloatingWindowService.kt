package com.operationrecorder.app.service

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import com.operationrecorder.app.R

class FloatingWindowService : Service() {
    
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var isRecording = false
    private var recordingStartTime = 0L
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onCreate() {
        super.onCreate()
        createFloatingWindow()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startRecording()
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        removeFloatingWindow()
    }
    
    private fun createFloatingWindow() {
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        
        // 创建悬浮窗布局
        floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window, null)
        
        // 设置窗口参数
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )
        
        params.gravity = Gravity.TOP or Gravity.START
        params.x = 100
        params.y = 100
        
        // 添加悬浮窗到窗口管理器
        windowManager?.addView(floatingView, params)
        
        // 设置悬浮窗的拖拽功能
        setupDragFunctionality(params)
        
        // 设置按钮点击事件
        setupButtonListeners()
    }
    
    private fun setupDragFunctionality(params: WindowManager.LayoutParams) {
        floatingView?.setOnTouchListener(object : View.OnTouchListener {
            private var initialX = 0
            private var initialY = 0
            private var initialTouchX = 0f
            private var initialTouchY = 0f
            
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()
                        windowManager?.updateViewLayout(floatingView, params)
                        return true
                    }
                }
                return false
            }
        })
    }
    
    private fun setupButtonListeners() {
        val btnStop = floatingView?.findViewById<Button>(R.id.btn_stop_recording)
        val btnPause = floatingView?.findViewById<Button>(R.id.btn_pause_recording)
        val btnClose = floatingView?.findViewById<Button>(R.id.btn_close_floating)
        
        btnStop?.setOnClickListener {
            stopRecording()
        }
        
        btnPause?.setOnClickListener {
            togglePauseRecording()
        }
        
        btnClose?.setOnClickListener {
            stopSelf()
        }
    }
    
    private fun startRecording() {
        isRecording = true
        recordingStartTime = System.currentTimeMillis()
        updateRecordingStatus()
        
        // 启动定时器更新录制时间
        startRecordingTimer()
    }
    
    private fun stopRecording() {
        isRecording = false
        AccessibilityRecorderService.stopRecording()
        stopSelf()
    }
    
    private fun togglePauseRecording() {
        // 这里可以实现暂停/恢复录制功能
        // 暂时简化处理
    }
    
    private fun updateRecordingStatus() {
        val statusText = floatingView?.findViewById<TextView>(R.id.tv_recording_status)
        val timeText = floatingView?.findViewById<TextView>(R.id.tv_recording_time)
        
        if (isRecording) {
            statusText?.text = "录制中..."
            statusText?.setTextColor(resources.getColor(android.R.color.holo_red_light, null))
        } else {
            statusText?.text = "已暂停"
            statusText?.setTextColor(resources.getColor(android.R.color.darker_gray, null))
        }
    }
    
    private fun startRecordingTimer() {
        val timeText = floatingView?.findViewById<TextView>(R.id.tv_recording_time)
        
        val timer = object : Runnable {
            override fun run() {
                if (isRecording) {
                    val elapsedTime = System.currentTimeMillis() - recordingStartTime
                    val seconds = elapsedTime / 1000
                    val minutes = seconds / 60
                    val hours = minutes / 60
                    
                    val timeString = String.format(
                        "%02d:%02d:%02d",
                        hours,
                        minutes % 60,
                        seconds % 60
                    )
                    
                    timeText?.text = timeString
                    
                    // 每秒更新一次
                    timeText?.postDelayed(this, 1000)
                }
            }
        }
        
        timeText?.post(timer)
    }
    
    private fun removeFloatingWindow() {
        floatingView?.let { view ->
            windowManager?.removeView(view)
            floatingView = null
        }
    }
}
