package com.operationrecorder.app

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.operationrecorder.app.adapter.ScriptAdapter
import com.operationrecorder.app.databinding.ActivityMainBinding
import com.operationrecorder.app.model.OperationScript
import com.operationrecorder.app.service.AccessibilityRecorderService
import com.operationrecorder.app.service.FloatingWindowService
import com.operationrecorder.app.utils.ScriptManager

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var scriptAdapter: ScriptAdapter
    private lateinit var scriptManager: ScriptManager
    private val scripts = mutableListOf<OperationScript>()
    
    companion object {
        private const val REQUEST_OVERLAY_PERMISSION = 1001
        private const val REQUEST_ACCESSIBILITY_PERMISSION = 1002
        private const val REQUEST_STORAGE_PERMISSION = 1003
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        scriptManager = ScriptManager(this)
        setupRecyclerView()
        setupClickListeners()
        checkPermissions()
        loadScripts()
    }
    
    private fun setupRecyclerView() {
        scriptAdapter = ScriptAdapter(scripts) { script ->
            // 执行脚本
            executeScript(script)
        }
        binding.recyclerViewScripts.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = scriptAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.btnStartRecording.setOnClickListener {
            if (hasAllPermissions()) {
                startRecording()
            } else {
                requestPermissions()
            }
        }
        
        binding.btnStopRecording.setOnClickListener {
            stopRecording()
        }
        
        binding.btnRefreshScripts.setOnClickListener {
            loadScripts()
        }
    }
    
    private fun checkPermissions() {
        if (!hasAllPermissions()) {
            requestPermissions()
        }
    }
    
    private fun hasAllPermissions(): Boolean {
        return hasOverlayPermission() && 
               hasAccessibilityPermission() && 
               hasStoragePermission()
    }
    
    private fun hasOverlayPermission(): Boolean {
        return Settings.canDrawOverlays(this)
    }
    
    private fun hasAccessibilityPermission(): Boolean {
        return AccessibilityRecorderService.isAccessibilityServiceEnabled(this)
    }
    
    private fun hasStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this, 
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun requestPermissions() {
        if (!hasOverlayPermission()) {
            requestOverlayPermission()
        } else if (!hasAccessibilityPermission()) {
            requestAccessibilityPermission()
        } else if (!hasStoragePermission()) {
            requestStoragePermission()
        }
    }
    
    private fun requestOverlayPermission() {
        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
        intent.data = Uri.parse("package:$packageName")
        startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION)
    }
    
    private fun requestAccessibilityPermission() {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        startActivityForResult(intent, REQUEST_ACCESSIBILITY_PERMISSION)
        Toast.makeText(this, "请在设置中启用无障碍服务", Toast.LENGTH_LONG).show()
    }
    
    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
            REQUEST_STORAGE_PERMISSION
        )
    }
    
    private fun startRecording() {
        if (!hasAllPermissions()) {
            Toast.makeText(this, "请先授予所有必要权限", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 启动悬浮窗服务
        val intent = Intent(this, FloatingWindowService::class.java)
        startService(intent)
        
        // 开始录制
        AccessibilityRecorderService.startRecording()
        
        binding.btnStartRecording.isEnabled = false
        binding.btnStopRecording.isEnabled = true
        
        Toast.makeText(this, "开始录制操作", Toast.LENGTH_SHORT).show()
    }
    
    private fun stopRecording() {
        // 停止录制
        AccessibilityRecorderService.stopRecording()
        
        // 停止悬浮窗服务
        val intent = Intent(this, FloatingWindowService::class.java)
        stopService(intent)
        
        binding.btnStartRecording.isEnabled = true
        binding.btnStopRecording.isEnabled = false
        
        Toast.makeText(this, "录制已停止", Toast.LENGTH_SHORT).show()
        
        // 刷新脚本列表
        loadScripts()
    }
    
    private fun loadScripts() {
        scripts.clear()
        scripts.addAll(scriptManager.getAllScripts())
        scriptAdapter.notifyDataSetChanged()
    }
    
    private fun executeScript(script: OperationScript) {
        if (!hasAccessibilityPermission()) {
            Toast.makeText(this, "需要无障碍服务权限来执行脚本", Toast.LENGTH_SHORT).show()
            return
        }
        
        AccessibilityRecorderService.executeScript(script)
        Toast.makeText(this, "开始执行脚本: ${script.name}", Toast.LENGTH_SHORT).show()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_OVERLAY_PERMISSION -> {
                if (hasOverlayPermission()) {
                    requestPermissions()
                }
            }
            REQUEST_ACCESSIBILITY_PERMISSION -> {
                if (hasAccessibilityPermission()) {
                    requestPermissions()
                }
            }
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_STORAGE_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                requestPermissions()
            }
        }
    }
}
