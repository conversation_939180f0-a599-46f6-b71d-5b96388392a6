@echo off
echo ========================================
echo    操作录制器 APK 构建脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请安装Java 8或更高版本
    pause
    exit /b 1
)

echo Java环境检查通过

:: 检查Android SDK
if not defined ANDROID_HOME (
    echo 警告: ANDROID_HOME环境变量未设置
    echo 请确保已安装Android SDK并设置环境变量
)

echo.
echo 开始构建APK...
echo.

:: 清理之前的构建
echo 清理之前的构建文件...
call gradlew.bat clean

if %errorlevel% neq 0 (
    echo 错误: 清理失败
    pause
    exit /b 1
)

:: 构建Debug APK
echo 构建Debug APK...
call gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo           构建成功！
echo ========================================
echo.
echo APK文件位置:
echo app\build\outputs\apk\debug\app-debug.apk
echo.

:: 检查APK文件是否存在
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK文件大小:
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do echo %%~zA 字节
    echo.
    echo 安装命令:
    echo adb install app\build\outputs\apk\debug\app-debug.apk
) else (
    echo 警告: APK文件未找到，构建可能失败
)

echo.
echo 按任意键退出...
pause >nul
