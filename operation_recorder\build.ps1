# PowerShell script to download Java and build APK
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Auto Setup and Build APK" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if Java exists
Write-Host "Checking Java environment..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Java environment exists, skipping download" -ForegroundColor Green
        $javaExists = $true
    } else {
        $javaExists = $false
    }
} catch {
    $javaExists = $false
}

if (-not $javaExists) {
    Write-Host "Java not found, downloading portable Java..." -ForegroundColor Yellow
    
    # Create temp directory
    if (-not (Test-Path "temp")) {
        New-Item -ItemType Directory -Path "temp" | Out-Null
    }
    
    Set-Location "temp"
    
    # Download OpenJDK 11
    Write-Host "Downloading OpenJDK 11..." -ForegroundColor Yellow
    $javaUrl = "https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.21%2B9/OpenJDK11U-jdk_x64_windows_hotspot_11.0.21_9.zip"
    
    try {
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        Invoke-WebRequest -Uri $javaUrl -OutFile "openjdk11.zip" -UseBasicParsing
        Write-Host "Java downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "Error: Failed to download Java" -ForegroundColor Red
        Write-Host "Please manually install Java and set JAVA_HOME" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Extract Java
    Write-Host "Extracting Java..." -ForegroundColor Yellow
    try {
        Expand-Archive -Path "openjdk11.zip" -DestinationPath "." -Force
        $javaDir = Get-ChildItem -Directory -Name "jdk-11*" | Select-Object -First 1
        
        if ($javaDir) {
            $env:JAVA_HOME = Join-Path $PWD $javaDir
            $env:PATH = "$($env:JAVA_HOME)\bin;$($env:PATH)"
            Write-Host "Java setup complete: $($env:JAVA_HOME)" -ForegroundColor Green
        } else {
            throw "Java directory not found"
        }
    } catch {
        Write-Host "Error: Failed to extract Java" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    Set-Location ".."
}

Write-Host ""
Write-Host "Starting APK build..." -ForegroundColor Yellow
Write-Host ""

# Clean previous build
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
try {
    & ".\gradlew.bat" clean
    if ($LASTEXITCODE -ne 0) {
        throw "Clean failed"
    }
    Write-Host "Clean completed" -ForegroundColor Green
} catch {
    Write-Host "Error: Clean failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Build APK
Write-Host "Building Debug APK..." -ForegroundColor Yellow
try {
    & ".\gradlew.bat" assembleDebug
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "Build completed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error: Build failed" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "1. Check network connection (dependencies need to be downloaded)" -ForegroundColor Yellow
    Write-Host "2. Ensure sufficient disk space" -ForegroundColor Yellow
    Write-Host "3. Check firewall settings" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           BUILD SUCCESSFUL!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$apkPath = "app\build\outputs\apk\debug\app-debug.apk"
if (Test-Path $apkPath) {
    Write-Host "APK location: $apkPath" -ForegroundColor Green
    
    # Copy APK to root directory for convenience
    Copy-Item $apkPath "operation-recorder.apk" -Force
    Write-Host "Copied to: operation-recorder.apk" -ForegroundColor Green
    Write-Host ""
    
    $fileSize = (Get-Item "operation-recorder.apk").Length
    Write-Host "File size: $fileSize bytes" -ForegroundColor Green
    Write-Host ""
    Write-Host "Install command:" -ForegroundColor Yellow
    Write-Host "adb install operation-recorder.apk" -ForegroundColor Cyan
} else {
    Write-Host "Warning: APK file not found" -ForegroundColor Red
}

# Clean up temp files
if (Test-Path "temp") {
    Write-Host ""
    Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
    Remove-Item "temp" -Recurse -Force
}

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Yellow
Read-Host
