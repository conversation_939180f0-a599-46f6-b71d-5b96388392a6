# 操作录制器 (Operation Recorder)

一个Android应用，可以录制用户的操作并生成脚本，然后将脚本转换成可执行的应用。

## 功能特性

- 📱 **操作录制**: 录制用户在屏幕上的点击、滑动、输入等操作
- 📝 **脚本生成**: 将录制的操作转换为JSON格式的脚本文件
- ▶️ **脚本执行**: 读取并执行保存的脚本，自动重复操作
- 🎯 **悬浮窗控制**: 录制时显示悬浮窗，方便控制录制过程
- 💾 **脚本管理**: 保存、加载、删除、重命名脚本文件
- 🔧 **权限管理**: 自动请求必要的系统权限

## 系统要求

- Android 7.0 (API 24) 或更高版本
- 无障碍服务权限
- 悬浮窗权限
- 存储权限

## 构建说明

### 前提条件

1. 安装 Android Studio 或 Android SDK
2. 安装 Java 8 或更高版本
3. 确保 ANDROID_HOME 环境变量已设置

### 构建步骤

1. 克隆或下载项目到本地
2. 打开命令行，进入项目目录
3. 运行构建命令：

```bash
# Windows
gradlew.bat assembleDebug

# Linux/Mac
./gradlew assembleDebug
```

4. 构建完成后，APK文件位于：
   `app/build/outputs/apk/debug/app-debug.apk`

### 安装APK

```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

## 使用说明

### 首次使用

1. 安装并启动应用
2. 应用会自动请求必要权限：
   - **无障碍服务权限**: 用于监听和执行操作
   - **悬浮窗权限**: 用于显示录制控制界面
   - **存储权限**: 用于保存脚本文件

### 录制操作

1. 点击"开始录制"按钮
2. 应用会显示悬浮窗，表示录制已开始
3. 在其他应用中执行你想要录制的操作
4. 点击悬浮窗中的"停止"按钮结束录制
5. 录制的脚本会自动保存到应用中

### 执行脚本

1. 在主界面的脚本列表中找到要执行的脚本
2. 点击脚本卡片上的"执行"按钮
3. 应用会自动执行录制的操作序列

### 脚本管理

- **查看脚本**: 脚本列表显示名称、创建时间、操作数量等信息
- **删除脚本**: 点击"删除"按钮移除不需要的脚本
- **编辑脚本**: 点击"编辑"按钮修改脚本信息（功能待完善）

## 技术架构

### 核心组件

- **MainActivity**: 主界面，负责权限管理和脚本列表显示
- **AccessibilityRecorderService**: 无障碍服务，负责录制和执行操作
- **FloatingWindowService**: 悬浮窗服务，提供录制控制界面
- **ScriptManager**: 脚本管理器，负责脚本的保存和加载
- **ScriptExecutor**: 脚本执行器，负责解析和执行脚本

### 数据模型

- **OperationScript**: 脚本数据模型
- **Operation**: 单个操作数据模型
- **OperationType**: 操作类型枚举

### 支持的操作类型

- 点击 (CLICK)
- 长按 (LONG_CLICK)
- 滑动 (SWIPE)
- 滚动 (SCROLL)
- 文本输入 (INPUT_TEXT)
- 按键事件 (KEY_EVENT)
- 应用启动 (APP_LAUNCH)
- 等待 (WAIT)

## 注意事项

1. **权限要求**: 应用需要无障碍服务权限才能正常工作
2. **兼容性**: 不同Android版本和设备可能存在兼容性差异
3. **性能影响**: 录制过程可能会轻微影响系统性能
4. **隐私保护**: 应用不会上传任何录制数据，所有数据仅存储在本地

## 故障排除

### 常见问题

1. **无法录制操作**
   - 检查是否已启用无障碍服务权限
   - 确认应用在无障碍设置中已启用

2. **悬浮窗不显示**
   - 检查是否已授予悬浮窗权限
   - 部分设备需要在设置中手动允许悬浮窗

3. **脚本执行失败**
   - 确认目标应用仍然安装在设备上
   - 检查界面布局是否发生变化

## 开发计划

- [ ] 添加脚本编辑功能
- [ ] 支持更多操作类型
- [ ] 添加脚本导入/导出功能
- [ ] 优化执行精度和稳定性
- [ ] 添加脚本调试功能

## 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
