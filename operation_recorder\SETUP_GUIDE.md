# 环境设置和构建指南

## 环境要求

### 1. Java Development Kit (JDK)

**下载和安装:**
- 下载 OpenJDK 11 或更高版本: https://adoptium.net/
- 或者下载 Oracle JDK: https://www.oracle.com/java/technologies/downloads/

**设置环境变量:**
1. 安装完成后，设置 `JAVA_HOME` 环境变量
2. 将 `%JAVA_HOME%\bin` 添加到 `PATH` 环境变量

**验证安装:**
```bash
java -version
javac -version
```

### 2. Android SDK

**选项1: 安装 Android Studio (推荐)**
- 下载: https://developer.android.com/studio
- 安装后会自动配置 Android SDK

**选项2: 仅安装 SDK Command Line Tools**
- 下载: https://developer.android.com/studio#command-tools
- 解压到合适的目录

**设置环境变量:**
1. 设置 `ANDROID_HOME` 环境变量指向 SDK 目录
2. 将以下路径添加到 `PATH`:
   - `%ANDROID_HOME%\platform-tools`
   - `%ANDROID_HOME%\tools`
   - `%ANDROID_HOME%\tools\bin`

**验证安装:**
```bash
adb version
```

## 构建步骤

### 方法1: 使用构建脚本 (Windows)

```bash
# 进入项目目录
cd operation_recorder

# 运行构建脚本
.\build_apk.bat
```

### 方法2: 使用 Gradle 命令

```bash
# 进入项目目录
cd operation_recorder

# 清理项目
.\gradlew.bat clean

# 构建 Debug APK
.\gradlew.bat assembleDebug

# 构建 Release APK (需要签名配置)
.\gradlew.bat assembleRelease
```

### 方法3: 使用 Android Studio

1. 打开 Android Studio
2. 选择 "Open an existing Android Studio project"
3. 选择 `operation_recorder` 目录
4. 等待项目同步完成
5. 点击 "Build" -> "Build Bundle(s) / APK(s)" -> "Build APK(s)"

## 输出文件位置

构建成功后，APK 文件将位于:
- Debug APK: `app/build/outputs/apk/debug/app-debug.apk`
- Release APK: `app/build/outputs/apk/release/app-release.apk`

## 安装APK

### 使用 ADB 安装

```bash
# 安装到连接的设备
adb install app/build/outputs/apk/debug/app-debug.apk

# 如果已安装，强制重新安装
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 直接安装

1. 将 APK 文件传输到 Android 设备
2. 在设备上启用"未知来源"安装
3. 点击 APK 文件进行安装

## 常见问题解决

### 1. "JAVA_HOME is not set" 错误

**解决方案:**
- 确保已安装 JDK
- 正确设置 JAVA_HOME 环境变量
- 重启命令行窗口

### 2. "SDK location not found" 错误

**解决方案:**
- 确保已安装 Android SDK
- 正确设置 ANDROID_HOME 环境变量
- 或在项目根目录创建 `local.properties` 文件:
```
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

### 3. 构建失败 - 依赖下载问题

**解决方案:**
- 检查网络连接
- 配置代理 (如果需要)
- 清理并重新构建:
```bash
.\gradlew.bat clean
.\gradlew.bat assembleDebug
```

### 4. 权限问题

**解决方案:**
- 确保有足够的磁盘空间
- 以管理员身份运行命令行
- 检查防病毒软件是否阻止了构建过程

## 签名配置 (Release 版本)

如需构建正式发布版本，需要配置签名:

1. 生成密钥库:
```bash
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

2. 在 `app/build.gradle` 中配置签名:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('my-release-key.keystore')
            storePassword 'your-store-password'
            keyAlias 'my-key-alias'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            // ... 其他配置
        }
    }
}
```

## 技术支持

如果遇到其他问题，请检查:
1. Android Studio 的构建日志
2. Gradle 的错误输出
3. 确保所有依赖都能正常下载

更多信息请参考:
- Android 开发者文档: https://developer.android.com/
- Gradle 用户指南: https://docs.gradle.org/current/userguide/userguide.html
