<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="操作录制器"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 录制控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/btn_start_recording"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录制"
            android:backgroundTint="@color/green"
            android:textColor="@android:color/white"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_stop_recording"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="停止录制"
            android:backgroundTint="@color/red"
            android:textColor="@android:color/white"
            android:enabled="false"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 脚本管理区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="已保存的脚本"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_refresh_scripts"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="刷新"
            android:backgroundTint="@color/blue"
            android:textColor="@android:color/white"
            style="?android:attr/buttonStyleSmall" />

    </LinearLayout>

    <!-- 脚本列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_scripts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/border_background"
        android:padding="8dp" />

    <!-- 状态信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="16dp"
        android:background="@drawable/info_background"
        android:padding="12dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用说明："
            android:textStyle="bold"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. 首次使用需要授予无障碍服务和悬浮窗权限"
            android:textSize="12sp"
            android:layout_marginBottom="2dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2. 点击'开始录制'后，应用会记录您的所有操作"
            android:textSize="12sp"
            android:layout_marginBottom="2dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3. 录制完成后，点击脚本可以重新执行相同的操作"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
