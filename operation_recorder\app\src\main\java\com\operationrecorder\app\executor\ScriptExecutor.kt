package com.operationrecorder.app.executor

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.content.Intent
import android.graphics.Path
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.operationrecorder.app.model.Operation
import com.operationrecorder.app.model.OperationScript
import com.operationrecorder.app.model.OperationType

class ScriptExecutor(
    private val accessibilityService: AccessibilityService,
    private val context: Context
) {
    
    companion object {
        private const val TAG = "ScriptExecutor"
    }
    
    private var isExecuting = false
    private var currentExecutionThread: Thread? = null
    private val handler = Handler(Looper.getMainLooper())
    
    /**
     * 执行脚本
     */
    fun executeScript(script: OperationScript, callback: ExecutionCallback? = null) {
        if (isExecuting) {
            callback?.onError("正在执行其他脚本，请稍后再试")
            return
        }
        
        isExecuting = true
        callback?.onStart(script)
        
        currentExecutionThread = Thread {
            try {
                executeScriptInternal(script, callback)
            } catch (e: InterruptedException) {
                Log.d(TAG, "Script execution interrupted")
                handler.post {
                    callback?.onCancelled()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing script", e)
                handler.post {
                    callback?.onError("脚本执行出错: ${e.message}")
                }
            } finally {
                isExecuting = false
                currentExecutionThread = null
            }
        }
        
        currentExecutionThread?.start()
    }
    
    /**
     * 停止当前执行的脚本
     */
    fun stopExecution() {
        if (isExecuting) {
            currentExecutionThread?.interrupt()
            isExecuting = false
        }
    }
    
    /**
     * 检查是否正在执行脚本
     */
    fun isExecuting(): Boolean = isExecuting
    
    private fun executeScriptInternal(script: OperationScript, callback: ExecutionCallback?) {
        val totalOperations = script.operations.size
        var completedOperations = 0
        
        for ((index, operation) in script.operations.withIndex()) {
            if (Thread.currentThread().isInterrupted) {
                throw InterruptedException()
            }
            
            // 更新进度
            handler.post {
                callback?.onProgress(completedOperations, totalOperations)
            }
            
            // 执行前等待
            if (operation.delayBefore > 0) {
                Thread.sleep(operation.delayBefore)
            }
            
            // 执行操作
            val success = executeOperation(operation)
            
            if (!success) {
                Log.w(TAG, "Operation failed: ${operation.type} at index $index")
                handler.post {
                    callback?.onOperationFailed(operation, index)
                }
            }
            
            // 执行后等待
            Thread.sleep(operation.delayAfter)
            
            completedOperations++
        }
        
        // 执行完成
        handler.post {
            callback?.onComplete(script)
        }
    }
    
    private fun executeOperation(operation: Operation): Boolean {
        return try {
            when (operation.type) {
                OperationType.CLICK -> {
                    performClick(operation.x, operation.y)
                }
                OperationType.LONG_CLICK -> {
                    performLongClick(operation.x, operation.y)
                }
                OperationType.SWIPE -> {
                    performSwipe(operation)
                }
                OperationType.SCROLL -> {
                    performScroll(operation)
                }
                OperationType.INPUT_TEXT -> {
                    performTextInput(operation.text)
                }
                OperationType.KEY_EVENT -> {
                    performKeyEvent(operation)
                }
                OperationType.APP_LAUNCH -> {
                    launchApp(operation.packageName)
                }
                OperationType.WAIT -> {
                    Thread.sleep(operation.delayAfter)
                    true
                }
                OperationType.SCREENSHOT -> {
                    // 截图功能可以在这里实现
                    true
                }
                else -> {
                    Log.w(TAG, "Unsupported operation type: ${operation.type}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing operation: ${operation.type}", e)
            false
        }
    }
    
    private fun performClick(x: Float, y: Float): Boolean {
        val path = Path()
        path.moveTo(x, y)
        
        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 100))
        
        return accessibilityService.dispatchGesture(gestureBuilder.build(), null, null)
    }
    
    private fun performLongClick(x: Float, y: Float): Boolean {
        val path = Path()
        path.moveTo(x, y)
        
        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 1000))
        
        return accessibilityService.dispatchGesture(gestureBuilder.build(), null, null)
    }
    
    private fun performSwipe(operation: Operation): Boolean {
        // 这里需要从operation中获取滑动的起始和结束坐标
        // 简化处理，使用固定的滑动距离
        val startX = operation.x
        val startY = operation.y
        val endX = startX + 200 // 向右滑动200像素
        val endY = startY
        
        val path = Path()
        path.moveTo(startX, startY)
        path.lineTo(endX, endY)
        
        val gestureBuilder = GestureDescription.Builder()
        gestureBuilder.addStroke(GestureDescription.StrokeDescription(path, 0, 300))
        
        return accessibilityService.dispatchGesture(gestureBuilder.build(), null, null)
    }
    
    private fun performScroll(operation: Operation): Boolean {
        // 查找可滚动的视图
        val rootNode = accessibilityService.rootInActiveWindow
        val scrollableNode = findScrollableNode(rootNode)
        
        return scrollableNode?.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD) ?: false
    }
    
    private fun performTextInput(text: String): Boolean {
        val rootNode = accessibilityService.rootInActiveWindow
        val focusedNode = findFocusedEditText(rootNode)
        
        return focusedNode?.let { node ->
            val arguments = android.os.Bundle()
            arguments.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
        } ?: false
    }
    
    private fun performKeyEvent(operation: Operation): Boolean {
        // 这里可以实现按键事件，比如返回键、Home键等
        // 需要根据具体的按键码来处理
        return true
    }
    
    private fun launchApp(packageName: String): Boolean {
        return try {
            val intent = context.packageManager.getLaunchIntentForPackage(packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(it)
                true
            } ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error launching app: $packageName", e)
            false
        }
    }
    
    private fun findScrollableNode(node: AccessibilityNodeInfo?): AccessibilityNodeInfo? {
        if (node == null) return null
        
        if (node.isScrollable) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            val result = findScrollableNode(child)
            if (result != null) return result
        }
        
        return null
    }
    
    private fun findFocusedEditText(node: AccessibilityNodeInfo?): AccessibilityNodeInfo? {
        if (node == null) return null
        
        if (node.isFocused && node.className == "android.widget.EditText") {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            val result = findFocusedEditText(child)
            if (result != null) return result
        }
        
        return null
    }
    
    /**
     * 脚本执行回调接口
     */
    interface ExecutionCallback {
        fun onStart(script: OperationScript)
        fun onProgress(completed: Int, total: Int)
        fun onOperationFailed(operation: Operation, index: Int)
        fun onComplete(script: OperationScript)
        fun onError(message: String)
        fun onCancelled()
    }
}
