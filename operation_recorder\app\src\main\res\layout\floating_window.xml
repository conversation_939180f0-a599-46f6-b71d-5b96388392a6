<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/floating_window_background"
    android:padding="12dp">

    <!-- 录制状态 -->
    <TextView
        android:id="@+id/tv_recording_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="录制中..."
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/holo_red_light"
        android:layout_gravity="center"
        android:layout_marginBottom="4dp" />

    <!-- 录制时间 -->
    <TextView
        android:id="@+id/tv_recording_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00:00"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_gravity="center"
        android:layout_marginBottom="8dp"
        android:fontFamily="monospace" />

    <!-- 控制按钮 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_pause_recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂停"
            android:textSize="12sp"
            android:backgroundTint="@color/orange"
            android:textColor="@android:color/white"
            android:layout_marginEnd="4dp"
            style="?android:attr/buttonStyleSmall" />

        <Button
            android:id="@+id/btn_stop_recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="停止"
            android:textSize="12sp"
            android:backgroundTint="@color/red"
            android:textColor="@android:color/white"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            style="?android:attr/buttonStyleSmall" />

        <Button
            android:id="@+id/btn_close_floating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="×"
            android:textSize="16sp"
            android:backgroundTint="@android:color/darker_gray"
            android:textColor="@android:color/white"
            android:layout_marginStart="4dp"
            style="?android:attr/buttonStyleSmall" />

    </LinearLayout>

</LinearLayout>
