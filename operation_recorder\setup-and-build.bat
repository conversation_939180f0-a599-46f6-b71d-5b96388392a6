@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    自动设置环境并构建APK
echo ========================================
echo.

:: 创建临时目录
if not exist "temp" mkdir temp
cd temp

:: 检查是否已有Java
echo 检查Java环境...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo Java环境已存在，跳过下载
    goto :build_apk
)

echo Java环境不存在，开始下载便携版Java...

:: 下载便携版OpenJDK 11
echo 下载OpenJDK 11...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.21%%2B9/OpenJDK11U-jdk_x64_windows_hotspot_11.0.21_9.zip' -OutFile 'openjdk11.zip'}"

if not exist "openjdk11.zip" (
    echo 错误: Java下载失败
    echo 请手动下载Java并设置JAVA_HOME环境变量
    pause
    exit /b 1
)

echo 解压Java...
powershell -Command "Expand-Archive -Path 'openjdk11.zip' -DestinationPath '.'"

:: 找到Java目录
for /d %%i in (jdk-11*) do set JAVA_DIR=%%i

if not defined JAVA_DIR (
    echo 错误: 无法找到Java目录
    pause
    exit /b 1
)

:: 设置临时Java环境
set JAVA_HOME=%CD%\%JAVA_DIR%
set PATH=%JAVA_HOME%\bin;%PATH%

echo Java设置完成: %JAVA_HOME%

:build_apk
cd ..

echo.
echo 开始构建APK...
echo.

:: 清理之前的构建
echo 清理之前的构建...
call gradlew.bat clean

if %errorlevel% neq 0 (
    echo 错误: 清理失败
    pause
    exit /b 1
)

:: 构建APK
echo 构建Debug APK...
call gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo 错误: 构建失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接（需要下载依赖）
    echo 2. 确保有足够的磁盘空间
    echo 3. 检查防火墙设置
    pause
    exit /b 1
)

echo.
echo ========================================
echo           构建成功！
echo ========================================
echo.

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
    
    :: 复制APK到根目录方便使用
    copy "app\build\outputs\apk\debug\app-debug.apk" "operation-recorder.apk"
    
    echo 已复制到: operation-recorder.apk
    echo.
    
    for %%A in ("operation-recorder.apk") do echo 文件大小: %%~zA 字节
    echo.
    echo 安装命令:
    echo adb install operation-recorder.apk
) else (
    echo 警告: APK文件未找到
)

:: 清理临时文件
if exist "temp" (
    echo.
    echo 清理临时文件...
    rmdir /s /q temp
)

echo.
echo 按任意键退出...
pause >nul
