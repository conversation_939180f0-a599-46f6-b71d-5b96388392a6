// This file is auto-generated by Android Data Binding.
// Do not modify this file -- YOUR CHANGES WILL BE ERASED!
package com.operationrecorder.app.databinding

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.viewbinding.ViewBinding
import com.operationrecorder.app.R

class ItemScriptBinding private constructor(
    private val rootView: View,
    val tvScriptName: TextView,
    val tvScriptTime: TextView,
    val tvScriptDescription: TextView,
    val tvOperationsCount: TextView,
    val tvDuration: TextView,
    val tvFileSize: TextView,
    val btnExecute: Button,
    val btnEdit: Button,
    val btnDelete: Button
) : ViewBinding {

    override fun getRoot(): View = rootView

    companion object {
        fun inflate(inflater: LayoutInflater): ItemScriptBinding {
            return inflate(inflater, null, false)
        }

        fun inflate(inflater: LayoutInflater, parent: ViewGroup?, attachToParent: Boolean): ItemScriptBinding {
            val root = inflater.inflate(R.layout.item_script, parent, attachToParent)
            return bind(root)
        }

        fun bind(rootView: View): ItemScriptBinding {
            val tvScriptName = rootView.findViewById<TextView>(R.id.tv_script_name)
            val tvScriptTime = rootView.findViewById<TextView>(R.id.tv_script_time)
            val tvScriptDescription = rootView.findViewById<TextView>(R.id.tv_script_description)
            val tvOperationsCount = rootView.findViewById<TextView>(R.id.tv_operations_count)
            val tvDuration = rootView.findViewById<TextView>(R.id.tv_duration)
            val tvFileSize = rootView.findViewById<TextView>(R.id.tv_file_size)
            val btnExecute = rootView.findViewById<Button>(R.id.btn_execute)
            val btnEdit = rootView.findViewById<Button>(R.id.btn_edit)
            val btnDelete = rootView.findViewById<Button>(R.id.btn_delete)

            return ItemScriptBinding(rootView, tvScriptName, tvScriptTime, tvScriptDescription, 
                tvOperationsCount, tvDuration, tvFileSize, btnExecute, btnEdit, btnDelete)
        }
    }
}
