<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 脚本名称和时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_script_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="脚本名称"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/tv_script_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2024-01-01 12:00"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray" />

        </LinearLayout>

        <!-- 脚本描述 -->
        <TextView
            android:id="@+id/tv_script_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="脚本描述信息"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 脚本统计信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_operations_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="操作数: 0"
                android:textSize="12sp"
                android:textColor="@color/blue" />

            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="时长: 0s"
                android:textSize="12sp"
                android:textColor="@color/green"
                android:gravity="center" />

            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="大小: 0KB"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:gravity="end" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_execute"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="执行"
                android:backgroundTint="@color/green"
                android:textColor="@android:color/white"
                android:layout_marginEnd="4dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_edit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="编辑"
                android:backgroundTint="@color/blue"
                android:textColor="@android:color/white"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                style="?android:attr/buttonStyleSmall" />

            <Button
                android:id="@+id/btn_delete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="删除"
                android:backgroundTint="@color/red"
                android:textColor="@android:color/white"
                android:layout_marginStart="4dp"
                style="?android:attr/buttonStyleSmall" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
