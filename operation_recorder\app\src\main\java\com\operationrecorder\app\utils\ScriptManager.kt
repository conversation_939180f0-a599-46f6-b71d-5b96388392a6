package com.operationrecorder.app.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.operationrecorder.app.model.OperationScript
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.io.IOException

class ScriptManager(private val context: Context) {
    
    companion object {
        private const val TAG = "ScriptManager"
        private const val SCRIPTS_FOLDER = "OperationRecorder"
        private const val SCRIPT_EXTENSION = ".json"
    }
    
    private val gson: Gson = GsonBuilder()
        .setPrettyPrinting()
        .create()
    
    private val scriptsDir: File by lazy {
        val dir = File(context.getExternalFilesDir(null), SCRIPTS_FOLDER)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        dir
    }
    
    /**
     * 保存脚本到文件
     */
    fun saveScript(script: OperationScript): <PERSON>olean {
        return try {
            val fileName = sanitizeFileName(script.name) + SCRIPT_EXTENSION
            val file = File(scriptsDir, fileName)
            
            FileWriter(file).use { writer ->
                gson.toJson(script, writer)
            }
            
            Log.d(TAG, "Script saved: ${file.absolutePath}")
            true
        } catch (e: IOException) {
            Log.e(TAG, "Error saving script: ${script.name}", e)
            false
        }
    }
    
    /**
     * 从文件加载脚本
     */
    fun loadScript(fileName: String): OperationScript? {
        return try {
            val file = File(scriptsDir, fileName)
            if (!file.exists()) {
                Log.w(TAG, "Script file not found: $fileName")
                return null
            }
            
            FileReader(file).use { reader ->
                gson.fromJson(reader, OperationScript::class.java)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading script: $fileName", e)
            null
        }
    }
    
    /**
     * 获取所有脚本
     */
    fun getAllScripts(): List<OperationScript> {
        val scripts = mutableListOf<OperationScript>()
        
        try {
            val files = scriptsDir.listFiles { file ->
                file.isFile && file.name.endsWith(SCRIPT_EXTENSION)
            }
            
            files?.forEach { file ->
                loadScript(file.name)?.let { script ->
                    scripts.add(script)
                }
            }
            
            // 按创建时间倒序排列
            scripts.sortByDescending { it.createdTime }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all scripts", e)
        }
        
        return scripts
    }
    
    /**
     * 删除脚本
     */
    fun deleteScript(scriptId: String): Boolean {
        return try {
            val files = scriptsDir.listFiles { file ->
                file.isFile && file.name.endsWith(SCRIPT_EXTENSION)
            }
            
            files?.forEach { file ->
                val script = loadScript(file.name)
                if (script?.id == scriptId) {
                    val deleted = file.delete()
                    Log.d(TAG, "Script deleted: ${file.name}, success: $deleted")
                    return deleted
                }
            }
            
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting script: $scriptId", e)
            false
        }
    }
    
    /**
     * 重命名脚本
     */
    fun renameScript(scriptId: String, newName: String): Boolean {
        return try {
            val files = scriptsDir.listFiles { file ->
                file.isFile && file.name.endsWith(SCRIPT_EXTENSION)
            }
            
            files?.forEach { file ->
                val script = loadScript(file.name)
                if (script?.id == scriptId) {
                    // 创建新的脚本对象
                    val updatedScript = script.copy(name = newName)
                    
                    // 删除旧文件
                    file.delete()
                    
                    // 保存新文件
                    return saveScript(updatedScript)
                }
            }
            
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error renaming script: $scriptId", e)
            false
        }
    }
    
    /**
     * 导出脚本到指定路径
     */
    fun exportScript(script: OperationScript, exportPath: String): Boolean {
        return try {
            val file = File(exportPath)
            FileWriter(file).use { writer ->
                gson.toJson(script, writer)
            }
            
            Log.d(TAG, "Script exported: $exportPath")
            true
        } catch (e: IOException) {
            Log.e(TAG, "Error exporting script to: $exportPath", e)
            false
        }
    }
    
    /**
     * 从指定路径导入脚本
     */
    fun importScript(importPath: String): OperationScript? {
        return try {
            val file = File(importPath)
            if (!file.exists()) {
                Log.w(TAG, "Import file not found: $importPath")
                return null
            }
            
            FileReader(file).use { reader ->
                val script = gson.fromJson(reader, OperationScript::class.java)
                
                // 保存到本地
                if (saveScript(script)) {
                    Log.d(TAG, "Script imported: ${script.name}")
                    script
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing script from: $importPath", e)
            null
        }
    }
    
    /**
     * 获取脚本存储目录
     */
    fun getScriptsDirectory(): File = scriptsDir
    
    /**
     * 清理文件名中的非法字符
     */
    private fun sanitizeFileName(fileName: String): String {
        return fileName.replace(Regex("[\\\\/:*?\"<>|]"), "_")
            .replace(Regex("\\s+"), "_")
            .take(50) // 限制文件名长度
    }
    
    /**
     * 获取脚本统计信息
     */
    fun getScriptStats(): ScriptStats {
        val scripts = getAllScripts()
        val totalOperations = scripts.sumOf { it.operations.size }
        val totalDuration = scripts.sumOf { it.duration }
        
        return ScriptStats(
            totalScripts = scripts.size,
            totalOperations = totalOperations,
            totalDuration = totalDuration,
            averageOperationsPerScript = if (scripts.isNotEmpty()) totalOperations / scripts.size else 0,
            averageDurationPerScript = if (scripts.isNotEmpty()) totalDuration / scripts.size else 0L
        )
    }
}

/**
 * 脚本统计信息数据类
 */
data class ScriptStats(
    val totalScripts: Int,
    val totalOperations: Int,
    val totalDuration: Long,
    val averageOperationsPerScript: Int,
    val averageDurationPerScript: Long
)
